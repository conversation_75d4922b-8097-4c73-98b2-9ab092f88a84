-- Test script for tms_get_capacity_wise_availability_overview_proto function
-- This script tests the availability overview function with sample data

DO $$
DECLARE
    test_org_id integer := 1;
    test_user_id uuid := '550e8400-e29b-41d4-a716-************';
    requester_json json;
    filters_json json;
    result json;
    start_date date := CURRENT_DATE;
    end_date date := CURRENT_DATE + INTERVAL '2 days';
BEGIN
    RAISE NOTICE 'Starting tests for tms_get_capacity_wise_availability_overview_proto function';
    RAISE NOTICE '----------------------------------------------------------------------';

    -- Create requester object
    requester_json := json_build_object(
        'org_id', test_org_id,
        'usr_id', test_user_id,
        'ip_address', '127.0.0.1',
        'user_agent', 'Test Agent'
    );

    -- Test Case 1: Basic filters with date range
    filters_json := json_build_object(
        'days', json_build_array(start_date::text, end_date::text),
        'locations', json_build_array(),
        'technician_roles', json_build_array(),
        'service_hubs', json_build_array()
    );

    RAISE NOTICE 'Test Case 1 - Basic date range filter';
    RAISE NOTICE 'Requester: %', requester_json;
    RAISE NOTICE 'Filters: %', filters_json;

    BEGIN
        result := tms_get_capacity_wise_availability_overview_proto(requester_json, filters_json);
        RAISE NOTICE 'Result: %', result;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error in Test Case 1: %', SQLERRM;
    END;

    RAISE NOTICE '----------------------------------------------------------------------';

    -- Test Case 2: Empty filters (should use current date)
    filters_json := json_build_object(
        'days', json_build_array(),
        'locations', json_build_array(),
        'technician_roles', json_build_array(),
        'service_hubs', json_build_array()
    );

    RAISE NOTICE 'Test Case 2 - Empty filters (current date)';
    RAISE NOTICE 'Filters: %', filters_json;

    BEGIN
        result := tms_get_capacity_wise_availability_overview_proto(requester_json, filters_json);
        RAISE NOTICE 'Result: %', result;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error in Test Case 2: %', SQLERRM;
    END;

    RAISE NOTICE '----------------------------------------------------------------------';

    -- Test Case 3: With specific filters
    filters_json := json_build_object(
        'days', json_build_array(start_date::text, end_date::text),
        'locations', json_build_array('1', '2'),
        'technician_roles', json_build_array('3', '4'),
        'service_hubs', json_build_array('1')
    );

    RAISE NOTICE 'Test Case 3 - With specific filters';
    RAISE NOTICE 'Filters: %', filters_json;

    BEGIN
        result := tms_get_capacity_wise_availability_overview_proto(requester_json, filters_json);
        RAISE NOTICE 'Result: %', result;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error in Test Case 3: %', SQLERRM;
    END;

    RAISE NOTICE '----------------------------------------------------------------------';
    RAISE NOTICE 'All tests completed!';

END $$;
