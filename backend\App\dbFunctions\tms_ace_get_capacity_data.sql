DROP FUNCTION IF EXISTS public.tms_ace_get_capacity_data(integer);

CREATE OR REPLACE FUNCTION public.tms_ace_get_capacity_data(p_org_id integer)
RETURNS TABLE (
    resource_id text,
    provider_id integer,
    vertical_id integer,
    vertical_name text,
    skill_id integer,
    skill_name varchar(20),
    hub_id bigint,
    hub_name varchar(100),
    start_time timestamp with time zone,
    end_time timestamp with time zone,
    total_capacity integer,
    available_capacity integer,
    booked_capacity numeric
)
LANGUAGE plpgsql AS $function$
DECLARE
    -- Declare variables for timestamps
    current_time_utc timestamp with time zone;
    end_time_utc timestamp with time zone;
BEGIN

    -- Check if the organization exists
    IF NOT EXISTS (SELECT 1 FROM public.cl_tx_orgs WHERE org_id = p_org_id) THEN
        RAISE EXCEPTION 'Organization not found';
    END IF;

    -- Check if capacity module is enabled for the organization
    IF NOT EXISTS (
        SELECT 1
        FROM public.cl_tx_orgs_settings
        WHERE org_id = p_org_id
        AND settings_type = 'ACE_CAPACITY_SETTINGS'
        AND (settings_data->>'enable_capacity_module')::boolean = true
    ) THEN
        RAISE EXCEPTION 'Capacity module is not enabled for this organization';
    END IF;


    -- Set the time variables in UTC
    -- For demonstration, we'll use a 3-hour window (9 AM to 12 PM)
    -- In production, you might want to adjust these times based on business requirements
    current_time_utc := CURRENT_DATE AT TIME ZONE 'UTC' + INTERVAL '18 hours'; -- 9 AM
    end_time_utc := CURRENT_DATE AT TIME ZONE 'UTC' + INTERVAL '20 hours'; -- 12 PM

    -- Provider_id = p_org_id
    -- Resource ID, A combination of <Provider_id><Vertical_id><Skill_id><Hub_id>
    -- Vertical list can be retreived as below , where org_id_ = p_org_id
    -- 	verticals_list = array_to_json(array(
    --    	select jsonb_build_object(
    -- 			'id',sp_orgs_settings.db_id,
    -- 		   )
    -- 	  from public.cl_tx_orgs_settings as sp_orgs_settings
    -- 	 where sp_orgs_settings.org_id = org_id_
    -- 	   and sp_orgs_settings.settings_type = 'SP_CUSTOM_FIELDS'
    -- 	 order by sp_orgs_settings.settings_data->>'vertical_title' asc
    -- ));

    -- Step 1:
    -- Forge a query to get unique vertical_id, skill_id, hub_id
    -- Step 2:
    -- Forge a query to get total_capacity, available_capacity, booked_capacity for each of the above


    -- Step 1: Get unique vertical_id, skill_id, hub_id combinations
    -- As per the comments, Resource ID is a combination of <Provider_id><Vertical_id><Skill_id><Hub_id>
    -- where provider_id is the p_org_id
    -- Using simple joins for better performance with large hub counts (up to 100,000)

    -- Step 2:
    -- Total capacity = number of active users whos primary_vertical, primary_skilll, primary_hub matches
    -- Available capacity = users who have marked themselves as available in the time frame
    -- Booked capacity = decimal sum of subtask overlaps with time frame (1.0 = full slot, 0.5 = half slot, etc.)
    -- Using simple joins for better performance with large data volumes
    RETURN QUERY
    SELECT p_org_id::text || '_' || vertical.db_id::text || '_' || skill.db_id::text || '_' || hub.id::text AS resource_id, -- Resource identifier
           p_org_id AS provider_id,
           vertical.db_id AS vertical_id,
           vertical.settings_data->>'vertical_title' AS vertical_name,
           skill.db_id AS skill_id,
           skill.skill_name AS skill_name,
           hub.id AS hub_id,
           hub.hub_name AS hub_name,
           current_time_utc AS start_time,
           end_time_utc AS end_time,
           COUNT(usr.usr_id)::integer AS total_capacity, -- Count users whose primary vertical, skill, and hub match this resource
           CEIL(SUM(CASE WHEN usr.usr_id IS NOT NULL
                       THEN tms_hlpr_calculate_user_availability(usr.usr_id, p_org_id, current_time_utc, end_time_utc)
                       ELSE 0
                  END))::integer AS available_capacity, -- Calculate availability using helper function and round up
           COALESCE(SUM(
               CASE WHEN sbtsk.db_id IS NOT NULL THEN
                   tms_hlpr_calculate_booked_capacity_overlap(
                       sbtsk.start_time,
                       sbtsk.end_time,
                       current_time_utc,
                       end_time_utc
                   )
               ELSE 0
               END
           ), 0)::numeric AS booked_capacity -- Calculate booked capacity as decimal based on time overlap using helper function
      FROM public.cl_tx_orgs_settings AS vertical
     INNER JOIN public.cl_tx_skill_map AS skill_map
        ON vertical.db_id = skill_map.vertical_id
     INNER JOIN public.cl_tx_skills AS skill
        ON skill.db_id = skill_map.skill_id
     INNER JOIN public.cl_tx_vertical_srvc_hubs AS hub
        ON vertical.db_id = hub.vertical_id
      LEFT JOIN public.cl_tx_users usr
        ON usr.org_id = p_org_id
       AND usr.is_active = true
       AND usr.primary_vertical = vertical.db_id
       AND usr.primary_srvc_hub = hub.id
       AND (usr.primary_skill1 = skill.db_id OR
             usr.primary_skill2 = skill.db_id OR
             usr.primary_skill3 = skill.db_id)
      LEFT JOIN public.cl_tx_sbtsk sbtsk
        ON usr.usr_id = ANY(sbtsk.assigned_to)
       AND sbtsk.start_time < end_time_utc
       AND sbtsk.end_time > current_time_utc
       AND sbtsk.is_deleted IS NOT TRUE
       AND sbtsk.org_id = p_org_id
     WHERE vertical.org_id = p_org_id
       AND vertical.settings_type = 'SP_CUSTOM_FIELDS'
       AND skill.isactive = true
       AND hub.org_id = p_org_id
       AND hub.is_active = true
     GROUP BY vertical.db_id, vertical.settings_data->>'vertical_title',
              skill.db_id, skill.skill_name,
              hub.id, hub.hub_name,
              current_time_utc, end_time_utc;






    -- Test data (for development and testing)
    -- RETURN QUERY
    -- SELECT
    --     'test_resource_' || i::text AS resource_id,
    --     i AS provider_id,
    --     i + 1 AS vertical_id,
    --     i + 2 AS skill_id,
    --     (i + 3)::bigint AS hub_id,
    --     NOW()::timestamp with time zone AS start_time,
    --     (NOW() + INTERVAL '1 hour')::timestamp with time zone AS end_time,
    --     10 AS total_capacity,
    --     8 AS available_capacity,
    --     2 AS booked_capacity
    -- FROM generate_series(1, 10) i;
END;
$function$;
