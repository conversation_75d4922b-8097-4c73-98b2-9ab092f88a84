import React, { useState, useEffect } from 'react';
import {
    Card,
    Checkbox,
    Select,
    Row,
    Col,
    Button,
    message,
    Spin,
    Alert,
    Tag,
    Radio,
} from 'antd';
import moment from 'moment';
import http_utils from '../../util/http_utils';

const { Option } = Select;

const BookingSlotUp = () => {
    const [isBookingRequired, setIsBookingRequired] = useState();
    const [selectedWeek, setSelectedWeek] = useState(null);
    const [weekOptions, setWeekOptions] = useState([]);
    const [weekData, setWeekData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [orgSettings, setOrgSettings] = useState(null);
    const [selectedSlots, setSelectedSlots] = useState({});

    // Generate week options
    useEffect(() => {
        generateWeekOptions();
    }, []);

    const generateWeekOptions = () => {
        const weeks = [];
        const today = moment();

        // Generate next 8 weeks starting from current week
        for (let i = 0; i < 5; i++) {
            const weekStart = today.clone().add(i, 'weeks').startOf('week');
            const weekEnd = weekStart.clone().endOf('week');

            weeks.push({
                value: `${weekStart.format('YYYY-MM-DD')}_${weekEnd.format('YYYY-MM-DD')}`,
                label: `${weekStart.format('Do MMM')} - ${weekEnd.format('Do MMM YYYY')}`,
                startDate: weekStart.format('YYYY-MM-DD'),
                endDate: weekEnd.format('YYYY-MM-DD'),
            });
        }

        setWeekOptions(weeks);
    };

    // Fetch org settings to get generated slots
    const fetchOrgSettings = async () => {
        try {
            setLoading(true);

            // First try to get availability slots configuration
            http_utils.performGetCall(
                '/setup/capacity/availability-slots/proto',
                {},
                (data) => {
                    console.log('Availability slots response:', data);
                    if (
                        data &&
                        data.form_data &&
                        data.form_data.generated_slots
                    ) {
                        setOrgSettings(data.form_data);
                    } else {
                        // Fallback to demo slots
                        console.log(
                            'No generated slots found, using demo slots'
                        );
                        setOrgSettings({ generated_slots: null });
                    }
                    setLoading(false);
                },
                (error) => {
                    console.error('Error fetching availability slots:', error);
                    // Fallback to demo slots on error
                    setOrgSettings({ generated_slots: null });
                    setLoading(false);
                }
            );
        } catch (error) {
            console.error('Error:', error);
            setOrgSettings({ generated_slots: null });
            setLoading(false);
        }
    };

    // Fetch week data when week is selected
    const fetchWeekData = async (weekValue) => {
        const selectedWeekOption = weekOptions.find(
            (w) => w.value === weekValue
        );
        if (!selectedWeekOption) return;

        try {
            setLoading(true);

            // Generate days for the selected week
            const startDate = moment(selectedWeekOption.startDate);
            const endDate = moment(selectedWeekOption.endDate);
            const days = [];

            let currentDate = startDate.clone();
            while (currentDate.isSameOrBefore(endDate)) {
                days.push({
                    date: currentDate.format('YYYY-MM-DD'),
                    dayName: currentDate.format('dddd'),
                    displayDate: currentDate.format('Do MMM'),
                });
                currentDate.add(1, 'day');
            }

            // Call API to get week-specific booking data (this would be a real API call)
            // For now, we'll just simulate it
            console.log(
                'Fetching booking data for week:',
                selectedWeekOption.startDate,
                'to',
                selectedWeekOption.endDate
            );

            // You can uncomment this to make an actual API call for week data
            /*
            http_utils.performGetCall(
                '/my-availability/range',
                {
                    start_date: selectedWeekOption.startDate,
                    end_date: selectedWeekOption.endDate
                },
                (weekApiData) => {
                    console.log('Week booking data:', weekApiData);
                    // Process the API response here
                },
                (error) => {
                    console.error('Error fetching week booking data:', error);
                }
            );
            */

            setWeekData({
                ...selectedWeekOption,
                days: days,
            });

            // Initialize selected slots for each day
            const initialSlots = {};
            days.forEach((day) => {
                initialSlots[day.date] = [];
            });
            setSelectedSlots(initialSlots);
        } catch (error) {
            console.error('Error fetching week data:', error);
            message.error('Failed to fetch week data');
        } finally {
            setLoading(false);
        }
    };

    // Handle week selection
    const handleWeekSelect = (value) => {
        setSelectedWeek(value);
        fetchWeekData(value);
    };

    // Handle slot selection for a specific day
    const handleSlotSelection = (date, slotValue) => {
        setSelectedSlots((prev) => {
            const daySlots = prev[date] || [];
            const isSelected = daySlots.includes(slotValue);

            return {
                ...prev,
                [date]: isSelected
                    ? daySlots.filter((slot) => slot !== slotValue)
                    : [...daySlots, slotValue],
            };
        });
    };

    // Get generated slots from org settings
    const getGeneratedSlots = () => {
        if (!orgSettings || !orgSettings.generated_slots) {
            // Return demo slots if org settings not available
            return [
                { value: '09:00AM - 10:00AM', label: '09:00AM - 10:00AM' },
                { value: '10:00AM - 11:00AM', label: '10:00AM - 11:00AM' },
                { value: '11:00AM - 12:00PM', label: '11:00AM - 12:00PM' },
                { value: '12:00PM - 01:00PM', label: '12:00PM - 01:00PM' },
                { value: '01:00PM - 02:00PM', label: '01:00PM - 02:00PM' },
                { value: '02:00PM - 03:00PM', label: '02:00PM - 03:00PM' },
                { value: '03:00PM - 04:00PM', label: '03:00PM - 04:00PM' },
                { value: '04:00PM - 05:00PM', label: '04:00PM - 05:00PM' },
            ];
        }

        return orgSettings.generated_slots;
    };

    // Handle booking requirement change
    // const handleBookingRequiredChange = (e) => {
    //      setIsBookingRequired(e);
    //     if (e) {
    //         fetchOrgSettings();
    //     } else {
    //         setSelectedWeek(null);
    //         setWeekData(null);
    //         setSelectedSlots({});
    //     }
    // };

    const handleBookingRequiredChange = (value) => {
        setIsBookingRequired(value);

        if (!value) {
            // Optional: Reset dependent data if user selects "No"
            setSelectedWeek(null);
            setSelectedSlots({});
        }
    };

    // Handle save selected slots
    const handleSaveSlots = () => {
        const totalSelectedSlots = Object.values(selectedSlots).reduce(
            (total, daySlots) => total + daySlots.length,
            0
        );

        if (totalSelectedSlots === 0) {
            message.warning('Please select at least one slot');
            return;
        }

        console.log('Selected slots:', selectedSlots);
        message.success(
            `Successfully saved ${totalSelectedSlots} slots across ${Object.keys(selectedSlots).filter((date) => selectedSlots[date].length > 0).length} days`
        );
    };

    return (
        <div>
            {/* Booking Required - Radio Buttons */}
            <div className="gx-mb-4">
                <div className="gx-mb-2">
                    <strong>Booking Required</strong>
                </div>
                <Radio.Group
                    value={isBookingRequired ? 'yes' : 'no'}
                    onChange={(e) =>
                        handleBookingRequiredChange(e.target.value === 'yes')
                    }
                >
                    <Radio value="yes">Yes</Radio>
                    <Radio value="no">No</Radio>
                </Radio.Group>
            </div>

            {isBookingRequired && (
                <>
                    <div className="gx-mb-4">
                        Select a slot
                        <Select
                            placeholder="Select a week"
                            style={{ width: '100%' }}
                            value={selectedWeek}
                            onChange={handleWeekSelect}
                            loading={loading}
                        >
                            {weekOptions.map((week) => (
                                <Option key={week.value} value={week.value}>
                                    {week.label}
                                </Option>
                            ))}
                        </Select>
                    </div>

                    {weekData && (
                        <div className="gx-mb-4 gx-ml-2">
                            {loading ? (
                                <div className="gx-text-center gx-p-4">
                                    <Spin size="large" />
                                </div>
                            ) : (
                                <>
                                    <Row gutter={[16, 16]}>
                                        {weekData.days.map((day) => (
                                            <Col
                                                xs={24}
                                                sm={12}
                                                md={8}
                                                lg={6}
                                                key={day.date}
                                            >
                                                <Card
                                                    size="small"
                                                    title={
                                                        <div>
                                                            <div>
                                                                {day.dayName},{' '}
                                                                {
                                                                    day.displayDate
                                                                }
                                                            </div>
                                                        </div>
                                                    }
                                                    className="gx-h-100 "
                                                >
                                                    <div
                                                        style={{
                                                            maxHeight: '200px',
                                                            overflowY: 'auto',
                                                        }}
                                                    >
                                                        {getGeneratedSlots().map(
                                                            (slot) => (
                                                                <Tag.CheckableTag
                                                                    key={`${day.date}-${slot.value}`}
                                                                    checked={selectedSlots[
                                                                        day.date
                                                                    ]?.includes(
                                                                        slot.value
                                                                    )}
                                                                    onChange={() =>
                                                                        handleSlotSelection(
                                                                            day.date,
                                                                            slot.value
                                                                        )
                                                                    }
                                                                    className="gx-mb-1"
                                                                >
                                                                    {slot.label ||
                                                                        slot.value}
                                                                </Tag.CheckableTag>
                                                            )
                                                        )}
                                                    </div>
                                                    {selectedSlots[day.date]
                                                        ?.length > 0 && (
                                                        <div className="gx-mt-2">
                                                            <small className="gx-text-primary">
                                                                {
                                                                    selectedSlots[
                                                                        day.date
                                                                    ].length
                                                                }{' '}
                                                                slot(s) selected
                                                            </small>
                                                        </div>
                                                    )}
                                                </Card>
                                            </Col>
                                        ))}
                                    </Row>

                                    <div className="gx-mt-4 gx-text-center">
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={handleSaveSlots}
                                            disabled={Object.values(
                                                selectedSlots
                                            ).every(
                                                (daySlots) =>
                                                    daySlots.length === 0
                                            )}
                                        >
                                            Save Selected Slots
                                        </Button>
                                    </div>
                                </>
                            )}
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default BookingSlotUp;
