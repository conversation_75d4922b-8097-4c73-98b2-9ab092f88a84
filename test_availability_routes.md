# Availability Report Routes Testing

## Route Configuration
- Base path: `/availability-report`
- Route file: `backend/App/routes/ace/v1_availability_report.js`

## Available Endpoints

### 1. Overview Proto
```
GET /availability-report/overview-proto
```
**Purpose**: Get availability overview statistics (present, limited, offline, total users)

**Query Parameters**:
- `filters`: JSON string with date range, locations, roles, service hubs
- `pagination`: JSON string (dummy)

**Example**:
```
GET /availability-report/overview-proto?filters={"days":["2024-12-04","2024-12-06"],"locations":[],"technician_roles":[],"service_hubs":[]}
```

### 2. Get All Availability
```
GET /availability-report/
```
**Purpose**: Get paginated list of availability data

**Query Parameters**:
- `page_no`: Page number
- `page_size`: Items per page
- `search_query`: Search term
- `filters`: JSON string with filters

**Example**:
```
GET /availability-report/?page_no=1&page_size=10&filters={"days":["2024-12-04","2024-12-06"]}
```

### 3. Export Availability
```
POST /availability-report/export
```
**Purpose**: Export availability data via email

**Body**:
```json
{
  "filters": "{\"days\":[\"2024-12-04\",\"2024-12-06\"],\"locations\":[],\"technician_roles\":[],\"service_hubs\":[]}",
  "email_id": "<EMAIL>",
  "subject": "Availability Report Export"
}
```

## Troubleshooting Steps

1. **Check if server is running**: Make sure your Node.js server is running
2. **Restart server**: If you made changes to route files, restart the server
3. **Check authentication**: Make sure you're authenticated (routes use AUTH_MIDDLEWARE)
4. **Check URL**: Ensure you're using the correct base URL (e.g., `http://localhost:3000/availability-report/overview-proto`)
5. **Check logs**: Look at server console logs for any errors

## Common Issues

1. **404 Not Found**: 
   - Server not restarted after route changes
   - Wrong URL path
   - Route file not properly exported

2. **500 Internal Server Error**:
   - Database connection issues
   - Missing database functions
   - Authentication/authorization issues

3. **Authentication Required**:
   - Missing or invalid JWT token
   - User not authorized for the organization

## Testing with curl

```bash
# Test overview endpoint
curl -X GET "http://localhost:3000/availability-report/overview-proto?filters={\"days\":[\"2024-12-04\",\"2024-12-06\"]}" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test list endpoint  
curl -X GET "http://localhost:3000/availability-report/?page_no=1&page_size=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```
