CREATE OR REPLACE FUNCTION public.tms_get_service_types_for_prvdr_vertical_update()
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    status boolean;
    message text;
    resp_data json;
begin 
    status := false;
    message := 'Internal_error';

    WITH temp_service_type_data AS (
        SELECT 
            srvc_type_id_element::integer AS srvc_type_id,
            orgs_settings.db_id AS vertical_id,
            orgs_settings.settings_data->>'vertical_title' AS vertical_title
        FROM cl_tx_orgs_settings AS orgs_settings
        JOIN LATERAL json_array_elements_text(orgs_settings.settings_data->'srvc_type_id') AS srvc_type_id_element ON TRUE
        WHERE orgs_settings.settings_type = 'SP_CUSTOM_FIELDS'
          AND orgs_settings.settings_data->'srvc_type_id' IS NOT NULL
        GROUP BY srvc_type_id_element, orgs_settings.db_id, orgs_settings.settings_data->>'vertical_title'
        ORDER BY srvc_type_id_element::integer
    )
    select array_to_json(array(
    	select json_build_object(
	        'srvc_type_id', tsd.srvc_type_id,
	        'vertical_id', tsd.vertical_id,
	        'vertical_title', tsd.vertical_title,
	        'count', COUNT(srvc_req.db_id)
        )
	     FROM temp_service_type_data tsd
	     LEFT JOIN cl_tx_srvc_req srvc_req
	       ON srvc_req.srvc_type_id = tsd.srvc_type_id
	      AND srvc_req.is_deleted IS NOT TRUE
	      AND srvc_req.prvdr_vertical IS NULL
	    GROUP BY tsd.srvc_type_id, tsd.vertical_id, tsd.vertical_title
	   HAVING COUNT(srvc_req.db_id) > 0
	    ORDER BY tsd.srvc_type_id
	))
	     INTO resp_data;

    IF resp_data IS NULL THEN
        resp_data := '[]'::json;
    END IF;

    status := true;
    message := 'success';

    RETURN json_build_object(
        'status', status,
        'code', message,
        'data', resp_data
    );

EXCEPTION
    WHEN OTHERS THEN
        status := false;
        message := 'Database error: ' || SQLERRM;

        RETURN json_build_object(
            'status', status,
            'message', message,
            'data', '[]'::json
        );
END;
$function$
;
